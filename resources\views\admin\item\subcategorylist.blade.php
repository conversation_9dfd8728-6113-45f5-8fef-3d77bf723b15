<html>

<head>
    <title>test</title>
</head>
<style type="text/css">
    body {
        font-family: 'Roboto Condensed', sans-serif;
    }

    .m-0 {
        margin: 0px;
    }

    .p-0 {
        padding: 0px;
    }

    .pt-5 {
        padding-top: 5px;
    }

    .mt-10 {
        margin-top: 10px;
    }

    .text-center {
        text-align: center !important;
    }

    .w-100 {
        width: 100%;
    }

    .w-50 {
        width: 50%;
    }

    .w-85 {
        width: 85%;
    }

    .w-15 {
        width: 15%;
    }

    .logo img {
        width: 200px;
        height: 60px;
    }

    .gray-color {
        color: #5D5D5D;
    }

    .text-bold {
        font-weight: bold;
    }

    .border {
        border: 1px solid black;
    }

    table tr,
    th,
    td {
        border: 1px solid #d2d2d2;
        border-collapse: collapse;
        padding: 7px 8px;
    }

    table tr th {
        background: #F4F4F4;
        font-size: 15px;
    }

    table tr td {
        font-size: 13px;
    }

    table {
        border-collapse: collapse;
    }

    .box-text p {
        line-height: 10px;
    }

    .float-left {
        float: left;
    }

    .total-part {
        font-size: 16px;
        line-height: 12px;
    }

    .total-right p {
        padding-right: 20px;
    }
</style>

<body>
    <div class="head-title">
        <h1 class="text-center m-0 p-0">{{ trans('labels.subcategorylist') }}</h1>
    </div>

    <div class="table-section bill-tbl w-100 mt-10">
        <table class="table w-100 mt-10 text-center">
            <tr>
                <th class="w-50">{{ trans('labels.cat_id') }}</th>
                <th class="w-50">{{ trans('labels.subcategory') }}</th>
                <th class="w-50">{{ trans('labels.id') }}</th>
            </tr>
            @foreach ($subcategorylist as $subcategory)
                <tr>
                    <td>
                        <div class="box-text">
                            {{ $subcategory->cat_id }}
                        </div>
                    </td>
                    <td>
                        <div class="box-text">
                            {{ $subcategory->subcategory_name }}
                        </div>
                    </td>
                    <td>
                        <div class="box-text">
                            {{ $subcategory->id }}
                        </div>
                    </td>
                </tr>
            @endforeach
        </table>
    </div>
</body>

</html>

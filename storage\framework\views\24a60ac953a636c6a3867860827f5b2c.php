
<meta name="theme-color" content="<?php echo e(helper::appdata()->web_primary_color ?? '#F82647'); ?>">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="apple-mobile-web-app-title" content="<?php echo e(helper::appdata()->title ?? 'Grandfood'); ?>">
<meta name="msapplication-TileImage" content="<?php echo e(helper::image_path(helper::appdata()->favicon ?? 'favicon.png')); ?>">
<meta name="msapplication-TileColor" content="<?php echo e(helper::appdata()->web_primary_color ?? '#F82647'); ?>">


<link rel="manifest" href="<?php echo e(url('/manifest.json')); ?>">


<link rel="apple-touch-icon" href="<?php echo e(helper::image_path(helper::appdata()->favicon ?? 'favicon.png')); ?>">
<link rel="apple-touch-icon" sizes="152x152" href="<?php echo e(helper::image_path(helper::appdata()->favicon ?? 'favicon.png')); ?>">
<link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(helper::image_path(helper::appdata()->favicon ?? 'favicon.png')); ?>">
<link rel="apple-touch-icon" sizes="167x167" href="<?php echo e(helper::image_path(helper::appdata()->favicon ?? 'favicon.png')); ?>">


<script>
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('/sw.js')
                .then(function(registration) {
                    console.log('ServiceWorker registration successful with scope: ', registration.scope);
                })
                .catch(function(error) {
                    console.log('ServiceWorker registration failed: ', error);
                });
        });
    }
</script>


<script>
    let deferredPrompt;
    let installButton = null;

    window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent Chrome 67 and earlier from automatically showing the prompt
        e.preventDefault();
        // Stash the event so it can be triggered later
        deferredPrompt = e;
        
        // Show install button if it exists
        if (installButton) {
            installButton.style.display = 'block';
        }
    });

    // Function to trigger PWA install
    function installPWA() {
        if (deferredPrompt) {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    console.log('User accepted the A2HS prompt');
                } else {
                    console.log('User dismissed the A2HS prompt');
                }
                deferredPrompt = null;
            });
        }
    }

    // Hide install button after successful installation
    window.addEventListener('appinstalled', (evt) => {
        console.log('PWA was installed');
        if (installButton) {
            installButton.style.display = 'none';
        }
    });
</script>
<?php /**PATH C:\xampp\htdocs\Grandfood\foodefy\resources\views/web/pwa/pwa.blade.php ENDPATH**/ ?>
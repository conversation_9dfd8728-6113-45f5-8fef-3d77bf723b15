{{-- Sales Notification Popup --}}
@if (helper::appdata()->fake_sales_notification == 1)
    <div id="sales-booster-popup" class="sales-notification-popup" style="display: none;">
        <div class="sales-notification-container">
            <div class="sales-notification-content">
                <div class="sales-notification-header">
                    <button type="button" class="sales-notification-close" onclick="closeSalesNotification()">
                        <i class="fa-solid fa-times"></i>
                    </button>
                </div>
                <div class="sales-notification-body" id="notification_body">
                    {{-- Content will be loaded via AJAX --}}
                </div>
            </div>
        </div>
    </div>

    {{-- Sales Notification Styles --}}
    <style>
        .sales-notification-popup {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 9999;
            max-width: 350px;
            animation: slideInUp 0.5s ease-out;
        }

        .sales-notification-container {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border: 1px solid #e0e0e0;
            overflow: hidden;
        }

        .sales-notification-header {
            position: relative;
            padding: 10px 15px 0;
        }

        .sales-notification-close {
            position: absolute;
            top: 5px;
            right: 10px;
            background: none;
            border: none;
            font-size: 16px;
            color: #999;
            cursor: pointer;
            padding: 5px;
            line-height: 1;
        }

        .sales-notification-close:hover {
            color: #333;
        }

        .sales-notification-body {
            padding: 15px;
        }

        .notification-item {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .notification-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--bs-primary, #F82647);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 600;
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
        }

        .notification-message {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .notification-time {
            font-size: 11px;
            color: #999;
            margin-top: 2px;
        }

        .notification-product {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .notification-product-image {
            width: 30px;
            height: 30px;
            border-radius: 4px;
            object-fit: cover;
        }

        .notification-product-name {
            font-size: 12px;
            font-weight: 500;
            color: #333;
        }

        @keyframes slideInUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideOutDown {
            from {
                transform: translateY(0);
                opacity: 1;
            }
            to {
                transform: translateY(100%);
                opacity: 0;
            }
        }

        .sales-notification-popup.closing {
            animation: slideOutDown 0.3s ease-in;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .sales-notification-popup {
                left: 10px;
                right: 10px;
                bottom: 10px;
                max-width: none;
            }
        }

        /* Loading state */
        .sales-notification-popup.loading .notification-body {
            opacity: 0.6;
        }

        .sales-notification-popup.loaded .notification-body {
            opacity: 1;
            transition: opacity 0.3s ease;
        }
    </style>

    {{-- Sales Notification JavaScript --}}
    <script>
        function closeSalesNotification() {
            const popup = document.getElementById('sales-booster-popup');
            if (popup) {
                popup.classList.add('closing');
                setTimeout(() => {
                    popup.style.display = 'none';
                    popup.classList.remove('closing');
                }, 300);
            }
        }

        // Auto hide after 5 seconds
        function autoHideSalesNotification() {
            setTimeout(() => {
                closeSalesNotification();
            }, 5000);
        }

        // Show notification with auto-hide
        function showSalesNotification() {
            const popup = document.getElementById('sales-booster-popup');
            if (popup) {
                popup.style.display = 'block';
                autoHideSalesNotification();
            }
        }
    </script>
@endif

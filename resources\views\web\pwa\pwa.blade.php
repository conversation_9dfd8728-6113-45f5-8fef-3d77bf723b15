{{-- PWA Meta Tags --}}
<meta name="theme-color" content="{{ helper::appdata()->web_primary_color ?? '#F82647' }}">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="apple-mobile-web-app-title" content="{{ helper::appdata()->title ?? 'Grandfood' }}">
<meta name="msapplication-TileImage" content="{{ helper::image_path(helper::appdata()->favicon ?? 'favicon.png') }}">
<meta name="msapplication-TileColor" content="{{ helper::appdata()->web_primary_color ?? '#F82647' }}">

{{-- PWA Manifest --}}
<link rel="manifest" href="{{ url('/manifest.json') }}">

{{-- Apple Touch Icons --}}
<link rel="apple-touch-icon" href="{{ helper::image_path(helper::appdata()->favicon ?? 'favicon.png') }}">
<link rel="apple-touch-icon" sizes="152x152" href="{{ helper::image_path(helper::appdata()->favicon ?? 'favicon.png') }}">
<link rel="apple-touch-icon" sizes="180x180" href="{{ helper::image_path(helper::appdata()->favicon ?? 'favicon.png') }}">
<link rel="apple-touch-icon" sizes="167x167" href="{{ helper::image_path(helper::appdata()->favicon ?? 'favicon.png') }}">

{{-- PWA Service Worker Registration --}}
<script>
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('/sw.js')
                .then(function(registration) {
                    console.log('ServiceWorker registration successful with scope: ', registration.scope);
                })
                .catch(function(error) {
                    console.log('ServiceWorker registration failed: ', error);
                });
        });
    }
</script>

{{-- PWA Install Prompt --}}
<script>
    let deferredPrompt;
    let installButton = null;

    window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent Chrome 67 and earlier from automatically showing the prompt
        e.preventDefault();
        // Stash the event so it can be triggered later
        deferredPrompt = e;
        
        // Show install button if it exists
        if (installButton) {
            installButton.style.display = 'block';
        }
    });

    // Function to trigger PWA install
    function installPWA() {
        if (deferredPrompt) {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    console.log('User accepted the A2HS prompt');
                } else {
                    console.log('User dismissed the A2HS prompt');
                }
                deferredPrompt = null;
            });
        }
    }

    // Hide install button after successful installation
    window.addEventListener('appinstalled', (evt) => {
        console.log('PWA was installed');
        if (installButton) {
            installButton.style.display = 'none';
        }
    });
</script>

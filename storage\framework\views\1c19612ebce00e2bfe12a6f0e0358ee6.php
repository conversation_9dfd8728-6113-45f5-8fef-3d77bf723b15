
<?php if(helper::getwhatsappmessage()->whatsapp_chat_on_off == 1): ?>
    <?php
        $whatsappConfig = helper::getwhatsappmessage();
        $whatsappNumber = $whatsappConfig->whatsapp_number ?? '';
        $position = $whatsappConfig->whatsapp_chat_position ?? 'right';
        $defaultMessage = urlencode('Hello! I would like to know more about your services.');
    ?>

    <div id="whatsapp-chat-widget" class="whatsapp-chat-widget whatsapp-position-<?php echo e($position); ?>">
        
        <div class="whatsapp-chat-button" onclick="toggleWhatsAppChat()">
            <i class="fab fa-whatsapp"></i>
            <span class="whatsapp-notification-badge" id="whatsapp-badge">1</span>
        </div>

        
        <div class="whatsapp-chat-box" id="whatsapp-chat-box" style="display: none;">
            <div class="whatsapp-chat-header">
                <div class="whatsapp-header-info">
                    <div class="whatsapp-avatar">
                        <img src="<?php echo e(helper::image_path(helper::appdata()->favicon ?? 'favicon.png')); ?>" alt="Support">
                        <span class="whatsapp-online-status"></span>
                    </div>
                    <div class="whatsapp-header-text">
                        <h6><?php echo e(helper::appdata()->title ?? 'Customer Support'); ?></h6>
                        <span class="whatsapp-status">Online</span>
                    </div>
                </div>
                <button class="whatsapp-close-btn" onclick="closeWhatsAppChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="whatsapp-chat-body">
                <div class="whatsapp-message whatsapp-message-received">
                    <div class="whatsapp-message-content">
                        <p>Hello! 👋</p>
                        <p>How can we help you today?</p>
                        <span class="whatsapp-message-time"><?php echo e(date('H:i')); ?></span>
                    </div>
                </div>
            </div>

            <div class="whatsapp-chat-footer">
                <div class="whatsapp-input-container">
                    <input type="text" id="whatsapp-message-input" placeholder="Type a message..." maxlength="500">
                    <button class="whatsapp-send-btn" onclick="sendWhatsAppMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="whatsapp-quick-actions">
                    <button class="whatsapp-quick-btn" onclick="sendQuickMessage('I want to place an order')">
                        🛒 Place Order
                    </button>
                    <button class="whatsapp-quick-btn" onclick="sendQuickMessage('What are your opening hours?')">
                        🕒 Opening Hours
                    </button>
                    <button class="whatsapp-quick-btn" onclick="sendQuickMessage('I need help with my order')">
                        ❓ Need Help
                    </button>
                </div>
            </div>
        </div>
    </div>

    
    <style>
        .whatsapp-chat-widget {
            position: fixed;
            bottom: 20px;
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .whatsapp-position-right {
            right: 20px;
        }

        .whatsapp-position-left {
            left: 20px;
        }

        .whatsapp-chat-button {
            width: 60px;
            height: 60px;
            background: #25D366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
            transition: all 0.3s ease;
            position: relative;
            animation: whatsapp-pulse 2s infinite;
        }

        .whatsapp-chat-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
        }

        .whatsapp-chat-button i {
            font-size: 28px;
            color: white;
        }

        .whatsapp-notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .whatsapp-chat-box {
            position: absolute;
            bottom: 80px;
            width: 350px;
            height: 450px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            animation: whatsapp-slide-up 0.3s ease-out;
        }

        .whatsapp-position-left .whatsapp-chat-box {
            left: 0;
        }

        .whatsapp-position-right .whatsapp-chat-box {
            right: 0;
        }

        .whatsapp-chat-header {
            background: #075E54;
            color: white;
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .whatsapp-header-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .whatsapp-avatar {
            position: relative;
        }

        .whatsapp-avatar img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .whatsapp-online-status {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #25D366;
            border: 2px solid white;
            border-radius: 50%;
        }

        .whatsapp-header-text h6 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .whatsapp-status {
            font-size: 12px;
            opacity: 0.8;
        }

        .whatsapp-close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
        }

        .whatsapp-chat-body {
            height: 280px;
            padding: 15px;
            overflow-y: auto;
            background: #ECE5DD;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .whatsapp-message {
            margin-bottom: 15px;
        }

        .whatsapp-message-received .whatsapp-message-content {
            background: white;
            padding: 10px 12px;
            border-radius: 15px 15px 15px 5px;
            max-width: 80%;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .whatsapp-message-content p {
            margin: 0 0 5px 0;
            font-size: 14px;
            line-height: 1.4;
        }

        .whatsapp-message-time {
            font-size: 11px;
            color: #999;
            float: right;
            margin-top: 5px;
        }

        .whatsapp-chat-footer {
            background: white;
            padding: 15px;
            border-top: 1px solid #eee;
        }

        .whatsapp-input-container {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .whatsapp-input-container input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }

        .whatsapp-send-btn {
            width: 40px;
            height: 40px;
            background: #25D366;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .whatsapp-quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .whatsapp-quick-btn {
            background: #f0f0f0;
            border: none;
            padding: 6px 10px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .whatsapp-quick-btn:hover {
            background: #e0e0e0;
        }

        @keyframes whatsapp-pulse {
            0% { box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4); }
            50% { box-shadow: 0 4px 12px rgba(37, 211, 102, 0.6), 0 0 0 10px rgba(37, 211, 102, 0.1); }
            100% { box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4); }
        }

        @keyframes whatsapp-slide-up {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .whatsapp-chat-box {
                width: 300px;
                height: 400px;
            }
            
            .whatsapp-position-right .whatsapp-chat-box {
                right: -10px;
            }
            
            .whatsapp-position-left .whatsapp-chat-box {
                left: -10px;
            }
        }
    </style>

    
    <script>
        let whatsappChatOpen = false;

        function toggleWhatsAppChat() {
            const chatBox = document.getElementById('whatsapp-chat-box');
            const badge = document.getElementById('whatsapp-badge');
            
            if (whatsappChatOpen) {
                closeWhatsAppChat();
            } else {
                chatBox.style.display = 'block';
                badge.style.display = 'none';
                whatsappChatOpen = true;
            }
        }

        function closeWhatsAppChat() {
            const chatBox = document.getElementById('whatsapp-chat-box');
            chatBox.style.display = 'none';
            whatsappChatOpen = false;
        }

        function sendWhatsAppMessage() {
            const input = document.getElementById('whatsapp-message-input');
            const message = input.value.trim();
            
            if (message) {
                openWhatsAppWithMessage(message);
                input.value = '';
            }
        }

        function sendQuickMessage(message) {
            openWhatsAppWithMessage(message);
        }

        function openWhatsAppWithMessage(message) {
            const whatsappNumber = '<?php echo e($whatsappNumber); ?>';
            const encodedMessage = encodeURIComponent(message);
            const whatsappUrl = `https://api.whatsapp.com/send?phone=${whatsappNumber}&text=${encodedMessage}`;
            window.open(whatsappUrl, '_blank');
            closeWhatsAppChat();
        }

        // Handle Enter key in input
        document.addEventListener('DOMContentLoaded', function() {
            const input = document.getElementById('whatsapp-message-input');
            if (input) {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendWhatsAppMessage();
                    }
                });
            }
        });

        // Close chat when clicking outside
        document.addEventListener('click', function(e) {
            const widget = document.getElementById('whatsapp-chat-widget');
            if (whatsappChatOpen && widget && !widget.contains(e.target)) {
                closeWhatsAppChat();
            }
        });
    </script>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Grandfood\foodefy\resources\views/web/whatsapp_chat.blade.php ENDPATH**/ ?>
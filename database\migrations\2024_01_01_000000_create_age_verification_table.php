<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('age_verification', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('age_verification_on_off')->default(0)->comment('0=Off, 1=On');
            $table->tinyInteger('popup_type')->default(1)->comment('1=Yes/No, 2=Date of Birth, 3=Age Input');
            $table->integer('min_age')->default(18);
            $table->text('age_verification_text')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('age_verification');
    }
};

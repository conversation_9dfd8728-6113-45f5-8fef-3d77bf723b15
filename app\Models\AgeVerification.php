<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AgeVerification extends Model
{
    use HasFactory;
    protected $table = 'age_verification';

    protected $fillable = [
        'age_verification_on_off',
        'popup_type',
        'min_age',
        'age_verification_text'
    ];

    // Provide default values for attributes that might not exist in the database
    protected $attributes = [
        'age_verification_on_off' => 0,
        'popup_type' => 1,
        'min_age' => 18,
        'age_verification_text' => 'You must be 18 or older to access this website.'
    ];
}

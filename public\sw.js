const CACHE_NAME = 'grandfood-v1';
const urlsToCache = [
    '/',
    '/storage/app/public/web-assets/css/bootstrap/bootstrap.min.css',
    '/storage/app/public/web-assets/css/style.css',
    '/storage/app/public/web-assets/js/jquery/jquery-3.6.0.js',
    '/storage/app/public/web-assets/js/bootstrap/bootstrap.bundle.min.js',
    '/storage/app/public/favicon.png'
];

// Install event
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('Opened cache');
                return cache.addAll(urlsToCache);
            })
    );
});

// Fetch event
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // Return cached version or fetch from network
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});

// Activate event
self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});

// Background sync for offline functionality
self.addEventListener('sync', function(event) {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

function doBackgroundSync() {
    // Handle background sync operations
    console.log('Background sync triggered');
}

// Push notification handling
self.addEventListener('push', function(event) {
    const options = {
        body: event.data ? event.data.text() : 'New notification from Grandfood',
        icon: '/storage/app/public/favicon.png',
        badge: '/storage/app/public/favicon.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        }
    };

    event.waitUntil(
        self.registration.showNotification('Grandfood', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', function(event) {
    console.log('Notification click received.');
    event.notification.close();
    event.waitUntil(
        clients.openWindow('/')
    );
});

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class CreateUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create customer user
        $customerUser = User::where('email', '<EMAIL>')->first();
        
        if ($customerUser) {
            // Update existing user
            $customerUser->password = Hash::make('peter1390');
            $customerUser->save();
            echo "Customer user password updated successfully!\n";
        } else {
            // Create new customer user
            User::create([
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('peter1390'),
                'type' => 2, // Customer type
                'is_available' => 1,
                'is_verified' => 1,
                'is_deleted' => 2, // Not deleted
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            echo "Customer user created successfully!\n";
        }
        
        // Also create/update admin user with same credentials
        $adminUser = User::where('email', '<EMAIL>')->first();
        
        if ($adminUser) {
            // Update existing admin
            $adminUser->password = Hash::make('peter1390');
            $adminUser->save();
            echo "Admin user password updated successfully!\n";
        } else {
            // Create new admin user
            User::create([
                'name' => 'Peter Njuguna Mungai (Admin)',
                'email' => '<EMAIL>',
                'password' => Hash::make('peter1390'),
                'type' => 1, // Admin type
                'is_available' => 1,
                'is_verified' => 1,
                'is_deleted' => 2, // Not deleted
                'license_type' => 'full', // Set license type to bypass checks
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            echo "Admin user created successfully!\n";
        }
    }
}

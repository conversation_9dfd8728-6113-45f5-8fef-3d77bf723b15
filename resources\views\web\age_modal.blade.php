{{-- Age Verification Modal --}}
@if (helper::getagedetails()->age_verification_on_off == 1)
    @php
        $ageDetails = helper::getagedetails();
        $popupType = $ageDetails->popup_type ?? 1; // 1=Yes/No, 2=DOB, 3=Age Input
        $minAge = $ageDetails->min_age ?? 18;
        $ageText = $ageDetails->age_verification_text ?? trans('labels.age_verification_text');
    @endphp

    {{-- Age Verification Modal --}}
    <div class="modal fade" id="age_modal" tabindex="-1" aria-labelledby="ageModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content age-verification-modal">
                <div class="modal-header border-0 text-center">
                    <div class="w-100">
                        <div class="age-verification-icon mb-3">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="modal-title fw-bold" id="ageModalLabel">{{ trans('labels.age_verification') }}</h4>
                    </div>
                </div>
                
                <div class="modal-body text-center px-4">
                    <div class="age-verification-content">
                        <p class="age-verification-text mb-4">{{ $ageText }}</p>
                        
                        {{-- Alert for age restriction --}}
                        <div class="alert alert-danger d-none" id="age-alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ trans('labels.age_alert') }}
                        </div>

                        {{-- Hidden inputs for JavaScript --}}
                        <input type="hidden" id="popup_type" value="{{ $popupType }}">
                        <input type="hidden" id="min_age" value="{{ $minAge }}">

                        {{-- Popup Type 1: Yes/No Buttons --}}
                        @if ($popupType == 1)
                            <div class="age-verification-buttons">
                                <p class="mb-4 fw-semibold">{{ trans('labels.yes_i_am') }}{{ $minAge }} {{ trans('labels.years_old') }}</p>
                                <div class="d-grid gap-3">
                                    <button type="button" class="btn btn-success btn-lg" onclick="ageverification()">
                                        <i class="fas fa-check me-2"></i>{{ trans('labels.yes') }}
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-lg" onclick="ageverificationcancel()">
                                        <i class="fas fa-times me-2"></i>{{ trans('labels.no') }}
                                    </button>
                                </div>
                            </div>
                        @endif

                        {{-- Popup Type 2: Date of Birth --}}
                        @if ($popupType == 2)
                            <div class="age-verification-dob">
                                <p class="mb-4 fw-semibold">{{ trans('labels.enter_dob') }}</p>
                                <div class="row g-2 mb-3">
                                    <div class="col-4">
                                        <select class="form-select" id="dd">
                                            <option value="">{{ trans('labels.day') }}</option>
                                            @for ($i = 1; $i <= 31; $i++)
                                                <option value="{{ sprintf('%02d', $i) }}">{{ $i }}</option>
                                            @endfor
                                        </select>
                                        <small class="text-danger d-none" id="dd-required">{{ trans('messages.day_required') }}</small>
                                    </div>
                                    <div class="col-4">
                                        <select class="form-select" id="mm">
                                            <option value="">{{ trans('labels.month') }}</option>
                                            @for ($i = 1; $i <= 12; $i++)
                                                <option value="{{ sprintf('%02d', $i) }}">{{ date('M', mktime(0, 0, 0, $i, 1)) }}</option>
                                            @endfor
                                        </select>
                                        <small class="text-danger d-none" id="mm-required">{{ trans('messages.month_required') }}</small>
                                    </div>
                                    <div class="col-4">
                                        <select class="form-select" id="yyyy">
                                            <option value="">{{ trans('labels.year') }}</option>
                                            @for ($i = date('Y'); $i >= (date('Y') - 100); $i--)
                                                <option value="{{ $i }}">{{ $i }}</option>
                                            @endfor
                                        </select>
                                        <small class="text-danger d-none" id="yyyy-required">{{ trans('messages.year_required') }}</small>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary btn-lg w-100" onclick="ageverification()">
                                    <i class="fas fa-check me-2"></i>{{ trans('labels.verify_age') }}
                                </button>
                            </div>
                        @endif

                        {{-- Popup Type 3: Age Input --}}
                        @if ($popupType == 3)
                            <div class="age-verification-input">
                                <p class="mb-4 fw-semibold">{{ trans('labels.enter_age') }}</p>
                                <div class="mb-3">
                                    <input type="number" class="form-control form-control-lg text-center" id="age" 
                                           placeholder="{{ trans('labels.your_age') }}" min="1" max="120">
                                    <small class="text-danger d-none" id="age-required">{{ trans('messages.age_required') }}</small>
                                </div>
                                <button type="button" class="btn btn-primary btn-lg w-100" onclick="ageverification()">
                                    <i class="fas fa-check me-2"></i>{{ trans('labels.verify_age') }}
                                </button>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="modal-footer border-0 justify-content-center">
                    <small class="text-muted">
                        <i class="fas fa-lock me-1"></i>
                        {{ trans('labels.your_information_is_secure') }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    {{-- Age Verification Styles --}}
    <style>
        .age-verification-modal {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        }

        .age-verification-modal .modal-header {
            background: linear-gradient(135deg, var(--bs-primary, #F82647) 0%, var(--bs-secondary, #FFC344) 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem 1.5rem 1.5rem;
        }

        .age-verification-icon {
            font-size: 3rem;
            opacity: 0.9;
        }

        .age-verification-modal .modal-title {
            font-size: 1.5rem;
            margin: 0;
        }

        .age-verification-modal .modal-body {
            padding: 2rem 1.5rem;
        }

        .age-verification-text {
            color: #666;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .age-verification-buttons .btn {
            font-weight: 600;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            transition: all 0.3s ease;
        }

        .age-verification-buttons .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .age-verification-dob .form-select,
        .age-verification-input .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: border-color 0.3s ease;
        }

        .age-verification-dob .form-select:focus,
        .age-verification-input .form-control:focus {
            border-color: var(--bs-primary, #F82647);
            box-shadow: 0 0 0 0.2rem rgba(248, 38, 71, 0.25);
        }

        .age-verification-modal .modal-footer {
            padding: 1rem 1.5rem 1.5rem;
        }

        /* Blur effect for main content */
        .blur {
            filter: blur(5px);
            pointer-events: none;
            user-select: none;
        }

        /* Animation for modal */
        .age-verification-modal {
            animation: ageModalSlideIn 0.4s ease-out;
        }

        @keyframes ageModalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-50px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Error state styling */
        .form-select.is-invalid,
        .form-control.is-invalid {
            border-color: #dc3545;
        }

        /* Success button hover effect */
        .btn-success:hover {
            background-color: #198754;
            border-color: #198754;
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .age-verification-modal .modal-body {
                padding: 1.5rem 1rem;
            }
            
            .age-verification-modal .modal-header {
                padding: 1.5rem 1rem 1rem;
            }
            
            .age-verification-icon {
                font-size: 2.5rem;
            }
            
            .age-verification-modal .modal-title {
                font-size: 1.25rem;
            }
        }
    </style>

    {{-- Apply blur effect to main content initially --}}
    <style>
        #main-content {
            filter: blur(5px);
            pointer-events: none;
            user-select: none;
            transition: filter 0.3s ease;
        }
    </style>
@endif

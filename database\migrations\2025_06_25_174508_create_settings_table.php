<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('settings')) {
            Schema::create('settings', function (Blueprint $table) {
                $table->id();
                $table->string('app_name')->nullable();
                $table->string('email')->nullable();
                $table->string('mobile')->nullable();
                $table->text('address')->nullable();
                $table->string('address_url')->nullable();
                $table->text('firebase')->nullable();
                $table->string('currency')->default('USD');
                $table->string('currency_position')->default('1');
                $table->string('currency_space')->default('1');
                $table->string('currency_formate')->default('1');
                $table->string('decimal_separator')->default('.');
                $table->string('time_format')->default('1');
                $table->string('date_format')->default('1');
                $table->decimal('referral_amount', 8, 2)->default(0);
                $table->integer('max_order_qty')->default(10);
                $table->decimal('min_order_amount', 8, 2)->default(0);
                $table->decimal('max_order_amount', 8, 2)->default(1000);
                $table->decimal('min_order_amount_for_free_shipping', 8, 2)->default(50);
                $table->decimal('shipping_charges', 8, 2)->default(0);
                $table->string('order_prefix')->default('ORD');
                $table->string('timezone')->default('UTC');
                $table->integer('order_number_start')->default(1);
                $table->tinyInteger('maintenance_mode')->default(2);
                $table->tinyInteger('online_table_booking')->default(2);
                $table->tinyInteger('login_required')->default(2);
                $table->tinyInteger('is_checkout_login_required')->default(2);
                $table->string('pickup_delivery')->default('both');
                $table->string('theme')->default('default');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};

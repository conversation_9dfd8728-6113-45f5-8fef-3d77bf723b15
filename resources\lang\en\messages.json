{"add_money_success": "Money Added to Your Wallet..", "success": "Success", "wrong": "Something went wrong.", "cannot_delete": "You can not delete this variation", "last_image": "You can not delete this image, This is last image", "cart_id_required": "Cart data is required", "item_required": "Item is required", "qty_required": "Qty is required", "restaurant_open": "The restaurant is open now.", "restaurant_closed": "Restaurant is closed. Try after some time.", "name_required": "Name is required", "first_name_required": "First name is required", "last_name_required": "Last name is required", "message_required": "Message is required", "user_required": "User id is required", "price_required": "Price is required", "blocked": "Your account has been blocked by Admin", "password_required": "Password required", "ratting_required": "Star selection required", "review_exist": "You already has given review! Try another", "old_password_required": "Old password required", "new_password_required": "New password required", "confirm_password_required": "Confirm password required", "new_password_diffrent": "New password must be different to old password", "mobile_required": "Mobile required", "no_data": "No data found", "favorite_available": "Item already in favorite list.", "favorite_not_exist": "<PERSON><PERSON> doesn't exist in favorite list.", "token_required": "Token is required", "google_id_required": "Google id is required", "facebook_id_required": "Facebook id is required", "otp_required": "OTP is required", "item_type_required": "Please select item type", "role_selection_required": "Please select role from options", "password_sent": "New Password Sent to your email", "unverified": "You haven't verified your email", "email_required": "Email is required", "guest_required": "Number of guests required", "noti_tune_required": "Sound File are Required", "noti_tune_must_mp3": "File Must be Mp3 Format", "email_error": "Something went wrong while sending email Please try again...", "email_sent": "OTP has been sent", "email_code": "Email Verification Code", "email_exist": "Email is already taken! Try another", "mobile_exist": "Mobile number is already taken! Try another", "invalid_email": "<PERSON><PERSON> is invalid", "invalid_user": "Invalid user!", "invalid_request": "Invalid request!", "invalid_otp": "Invalid OTP", "invalid_referral_code": "Invalid Referral Code", "invalid_cart": "Invalid cart!", "invalid_address": "Invalid address!", "invalid_promocode": "Coupon code is wrong or currently unavailable", "invalid_category": "Invalid category", "email_pass_invalid": "Email or password is invalid! Please try again", "old_password_invalid": "Old password is invalid! Please try again", "online_note": "Restaurant is currently online. Switch to make it offline", "offline_note": "Restaurant is currently offline. Switch to make it online", "remove_cartitem_note": "The product has multiple customizations added. Go to cart to remove product from cart.", "enter_identity_type": "Identification type like Passport,Driving License,Voter..", "link_required": "Social media link required", "confirm_password_same": "Confirm password must be same as new password", "accept_terms": "Please accept our terms and conditions", "driver_assigned_title": "Delivery boy assigned", "enter_area": "Apartment / Road / Area (Optional)", "enter_house_no": "House / Flat / Floor No.", "payment_selection_required": "Please select payment type", "date_required": "Date required", "time_required": "Time required", "reservation_type_required": "Please enter reservation type", "table_number_required": "Please enter table number", "offercode_required": "Coupon code is required", "once_per_user": "The Coupon Is Applicable Only Once Per User", "usage_limit_exceeded": "Coupon usage limit has been exceeded.", "offer_expired": "Coupon has been expired.", "invalid_order": "Invalid order!", "order_amount_greater_then": "Order Amount must be greater then ", "order_type_selection_required": "Order type selection is required", "new_order_assigned_title": "New order assigned", "order_type_required": "Order type is required", "cart_is_empty": "Look like your cart is empty! Pls add some item in cart to make an order", "order_id_required": "Order id required", "already_acceped_by_admin": "Order olready acceped by admin", "already_cancelled_by_admin": "Order olready cancelled by admin", "already_cancelled_by_you": "Order olready cancelled by you", "amount_validation_msg": "Total amount must be less than recived amount!!", "new_order_arrive": "New Order Arrived...", "grand_total_required": "Grand total required", "transaction_type_required": "Transaction type is required", "address_type_required": "Address type is required", "delivery_date_required": "Delivery Date is required", "delivery_time_required": "Delivery Time is required", "pickup_date_required": "Pickup Date is required", "pickup_time_required": "Pickup Time is required", "address_required": "Address is required", "landmark_required": "Landmark is required", "city_required": "City is required", "state_required": "State is required", "country_required": "Country is required", "pincode_required": "Pincode is required", "select_address": "Please select the address", "insufficient_wallet": "Insufficient balance in wallet!", "unable_to_complete_payment": "Something went wrong while making payment", "transaction_id_required": "Transaction id required", "card_number": "card number required", "card_exp_month": "card exp month required", "card_exp_year": "card exp year required", "card_cvc": "card cvc required", "select_driver": "Select driver", "enter_attribute": "Enter attribute ex. color, size ..", "enter_amount": "Enter amount", "rolename_required": "Please enter role name", "one_selection_required": "Please select one option from given options", "first_name": "First Name", "last_name": "Last Name", "guests_required": "No of guests are required", "identity_type_required": "Please enter identity type", "identity_number_required": "Please enter identity number", "delivery_charge_required": "Please enter delivery charge", "be_up_to_date": "Be always up to date", "amount_required": "Please enter amount", "amount_less_then": "Amount must be less then ", "content_required": "Please enter content", "settings_note": "Click to set general settings", "category_required": "Category required", "subcategory_required": "Sub category required", "preparation_time_required": "Please select item preparation time", "session_id_or_user_id_required": "Invalid session id or user id", "item_name_required": "Please enter item name", "item_id_required": "Please enter item id", "buynow_required": "Please enter buynow type", "addons_id_required": "Please enter addons id", "addons_price_required": "Please enter addons price", "attribute_required": "Please enter item attribute", "image_required": "Please upload an image file", "variation_name_required": "Please enter item variation name", "product_price_required": "Please enter product price", "numbers_only": "Please enter numbers/digits only", "enter_image_file": "please select an image type of file to upload", "valid_image": "select only jpg/jpeg/png type of image file to upload", "category_name_required": "Please enter category name", "subcategory_name_required": "Please enter sub-category name", "addons_name_required": "Please enter addons name", "addongroup_id_required": "Please select addon group", "type_required": "Please select type", "title_required": "Please enter title", "subtitle_required": "Please enter subtitle", "designation_required": "Please enter designation", "description_required": "Please enter description", "offer_name_required": "Please enter offer name", "offer_type_required": "Please select offer type", "offer_amount_required": "Please enter offer amount", "min_amount_required": "Please enter min amount", "start_date_required": "Please select promocode start date", "expire_date_required": "Please select promocode end/expire date", "delivery_not_available": "Sorry, currently we are not delivering in your address", "out_of_working_hours": "You can not perform this operation due to out of working hours", "are_you_sure": "Are you sure?", "record_safe": "Your data is safe :)", "yes": "Yes", "no": "Cancel", "order_placed_note": "Your order has been placed successfully & will be process by system.", "are_you_sure_logout": "Are you sure?", "order_amount_must_between": "Order amount must be between", "order_qty_less_then": "Order qty must be less then", "cancel_order_note": "Are you sure to cancel this order? If yes, then order amount (Online payment OR Wallet payment) will be transferred to your wallet.", "lat_required": "Latitude is required", "lang_required": "Longitude is required", "max_order_qty_required": "Max order qty is required", "min_order_amount_required": "Min order amount is required", "max_order_amount_required": "Max order amount is required", "order_number_start_required": "Order number start is required", "order_prefix_required": "Order prefix is required", "already_added_this_status": "You Have <PERSON><PERSON><PERSON> Added this type", "map_required": "Map key is required", "firebase_required": "Firebase is required", "referral_amount_required": "Referral amount is required", "dimensions_required": "The pwa app logo has invalid image dimensions.", "google_client_id_required": "The google client id is required.", "google_client_secret_required": "The google client secret id is required.", "google_redirect_url_required": "The google redirect url is required.", "facebook_client_id_required": "The facebook client id is required.", "facebook_client_secret_required": "The facebook client secret id is required.", "facebook_redirect_url_required": "The facebook redirect url is required.", "mail_driver_required": "Mail driver is required.", "mail_host_required": "Mail host is required.", "mail_port_required": "Mail port is required.", "mail_username_required": "Mail username is required.", "mail_password_required": "Mail password is required.", "mail_encryption_required": "Mail encryption is required.", "mail_fromaddress_required": "Mail from address is required.", "mail_fromname_required": "Mail from name is required.", "test_mail_fail_message": "Your email SMTP details are not correct. Please enter correct details to send test email", "recaptcha_version_required": "Recaptcha version is required", "google_recaptcha_site_key_required": "Google recaptcha site key is required", "google_recaptcha_secret_key_required": "Google recaptcha secret key is required", "tax_name_required": "Please enter tax name", "tax_required": "Please enter tax", "product_added_successfully": "added successfully in cart."}
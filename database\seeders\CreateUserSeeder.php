<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\AgeVerification;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class CreateUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create customer user
        $customerUser = User::where('email', '<EMAIL>')->first();

        if ($customerUser) {
            // Update existing user
            $customerUser->password = Hash::make('peter1390');
            $customerUser->save();
            echo "Customer user password updated successfully!\n";
        } else {
            // Create new customer user
            User::create([
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('peter1390'),
                'type' => 2, // Customer type
                'is_available' => 1,
                'is_verified' => 1,
                'is_deleted' => 2, // Not deleted
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            echo "Customer user created successfully!\n";
        }

        // Also create/update admin user with same credentials
        $adminUser = User::where('email', '<EMAIL>')->first();

        if ($adminUser) {
            // Update existing admin
            $adminUser->password = Hash::make('peter1390');
            $adminUser->save();
            echo "Admin user password updated successfully!\n";
        } else {
            // Create new admin user
            User::create([
                'name' => 'Peter Njuguna Mungai (Admin)',
                'email' => '<EMAIL>',
                'password' => Hash::make('peter1390'),
                'type' => 1, // Admin type
                'is_available' => 1,
                'is_verified' => 1,
                'is_deleted' => 2, // Not deleted
                'license_type' => 'full', // Set license type to bypass checks
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            echo "Admin user created successfully!\n";
        }

        // Create age verification settings if they don't exist
        $ageVerification = AgeVerification::first();
        if (!$ageVerification) {
            try {
                AgeVerification::create([
                    'age_verification_on_off' => 0, // Disabled by default
                    'popup_type' => 1, // Yes/No type
                    'min_age' => 18, // Default minimum age
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                echo "Age verification settings created successfully!\n";
            } catch (\Exception $e) {
                echo "Error creating age verification settings: " . $e->getMessage() . "\n";
                // Try with minimal data
                try {
                    $ageVerification = new AgeVerification();
                    $ageVerification->age_verification_on_off = 0;
                    $ageVerification->save();
                    echo "Age verification settings created with minimal data!\n";
                } catch (\Exception $e2) {
                    echo "Failed to create age verification settings: " . $e2->getMessage() . "\n";
                }
            }
        } else {
            echo "Age verification settings already exist.\n";
        }
    }
}

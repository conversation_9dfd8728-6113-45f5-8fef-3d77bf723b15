(function(b){b.easyAjax=function(l){var f={type:"GET",container:"body",blockUI:true,disableButton:false,buttonSelector:"[type='submit']",dataType:"json",messagePosition:"toastr",errorPosition:"field",hideElements:false,redirect:true,data:{},file:false};var d=f;if(l){d=b.extend(f,l)}if(typeof d.beforeSend!="function"){d.beforeSend=function(){b(d.container).find(".has-error").each(function(){b(this).find(".help-block").text("");b(this).removeClass("has-error")});b(d.container).find("#alert").html("");if(d.blockUI){b.easyBlockUI(d.container)}if(d.disableButton){j(d.buttonSelector)}}}if(typeof d.complete!="function"){d.complete=function(i,m){if(d.blockUI){b.easyUnblockUI(d.container)}if(d.disableButton){c(d.buttonSelector)}}}if(typeof d.error!="function"){d.error=function(i,o,m){var n="A server side error occurred. Please try again after sometime.";if(o=="timeout"){n="Connection timed out! Please check your internet connection"}e(n,"error")}}function e(q,n,m){var p={error:"danger",success:"success",primary:"primary",warning:"warning",info:"info"};if(d.messagePosition=="toastr"){b.showToastr(q,n,m)}else{var o=b(d.container).find("#alert");var i='<div class="alert alert-'+p[n]+'">'+q+"</div>";if(o.length==0){b(d.container).find(".form-group:first").before('<div id="alert">'+i+"</div>")}else{o.html(i)}}}if(d.file==true){var g=new FormData(b(d.container)[0]);var k=Object.keys(d.data);for(var h=0;h<k.length;h++){g.append(k[h],d.data[k[h]])}d.data=g}b.ajax({type:d.type,url:d.url,dataType:d.dataType,data:d.data,beforeSend:d.beforeSend,contentType:(d.file)?false:"application/x-www-form-urlencoded; charset=UTF-8",processData:!d.file,error:d.error,complete:d.complete,success:function(m){if(m.status=="success"){if(m.action=="redirect"){if(d.redirect){var x="";if(typeof m.message!="undefined"){x+=m.message}x+=" Redirecting...";e(x,"success",{timeOut:100000,positionClass:"toast-top-center"});window.location.href=m.url}}else{if(typeof m.message!="undefined"){e(m.message,"success")}}if(d.removeElements==true){b(d.container).find(".form-group, button, input").remove()}}if(m.status=="fail"){if(typeof m.message!="undefined"){e(m.message,"error")}if(typeof m.errors!="undefined"){var u=Object.keys(m.errors);b(d.container).find(".has-error").find(".help-block").remove();b(d.container).find(".has-error").removeClass("has-error");if(d.errorPosition=="field"){for(var p=0;p<u.length;p++){var s=u[p].replace(".","\\.");var w=b(d.container).find("[name='"+s+"']");if(w.length==0){w=b(d.container).find("#"+s)}var t=w.closest(".form-group");b(t).find(".help-block").remove();var n=b(t).find("div:first");if(n.length==0){n=b(t)}n.append('<div class="help-block">'+m.errors[u[p]]+"</div>");b(t).addClass("has-error")}if(u.length>0){var o=b("[name='"+u[0]+"']");if(o.length>0){b("html, body").animate({scrollTop:o.offset().top-150},200)}}}else{var r="<ul>";for(var p=0;p<u.length;p++){r+="<li>"+m.errors[u[p]]+"</li>"}r+="</ul>";var v=b(d.container).find("#alert");var q='<div class="alert alert-danger">'+r+"</div>";if(v.length==0){b(d.container).find(".form-group:first").before('<div id="alert">'+q+"</div>")}else{v.html(q)}}}}if(typeof d.success=="function"){d.success(m)}}});function j(i){var m=b(d.container).find(i);var n="Submitting...";if(m.width()<20){n="..."}if(!m.is("input")){m.attr("data-prev-text",m.html());m.text(n);m.prop("disabled",true)}else{m.attr("data-prev-text",m.val());m.val(n);m.prop("disabled",true)}}function c(i){var m=b(d.container).find(i);if(!m.is("input")){m.html(m.attr("data-prev-text"));m.prop("disabled",false)}else{m.val(m.attr("data-prev-text"));m.prop("disabled",false)}}};b.easyBlockUI=function(c,f){if(f==undefined){f="Loading..."}var d='<div class="loading-message"><div class="block-spinner-bar"><div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div></div></div>';if(c!=undefined){var e=b(c);var g=false;if(e.height()<=(b(window).height())){g=true}e.block({message:d,baseZ:999999,centerY:g,css:{top:"10%",border:"0",padding:"0",backgroundColor:"none"},overlayCSS:{backgroundColor:"transparent",opacity:0.05,cursor:"wait"}})}else{b.blockUI({message:d,baseZ:999999,css:{border:"0",padding:"0",backgroundColor:"none"},overlayCSS:{backgroundColor:"#555",opacity:0.05,cursor:"wait"}})}};b.easyUnblockUI=function(c){if(c==undefined){b.unblockUI()}else{b(c).unblock({onUnblock:function(){b(c).css("position","");b(c).css("zoom","")}})}};b.showToastr=function(e,g,c){var f={closeButton:false,debug:false,positionClass:"toast-top-right",onclick:null,showDuration:"1000",hideDuration:"1000",timeOut:"5000",extendedTimeOut:"1000",showEasing:"swing",hideEasing:"linear",showMethod:"fadeIn",hideMethod:"fadeOut"};var d=f;if(typeof c=="object"){d=b.extend(f,c)}toastr.options=d;g=typeof g!=="undefined"?g:"success";toastr[g](e)};b.ajaxModal=function(c,d,e){b(c).removeData("bs.modal").modal({remote:d,show:true});b(document).trigger("ajaxPageLoad");if(typeof e!="undefined"){e()}b(c).on("hidden.bs.modal",function(){b(this).find(".modal-body").html("Loading...");b(this).find(".modal-footer").html('<button type="button" data-dismiss="modal" class="btn dark btn-outline">Cancel</button>');b(this).data("bs.modal",null)})};b.showErrors=function(d){var f=Object.keys(d);b(".has-error").find(".help-block").remove();b(".has-error").removeClass("has-error");for(var e=0;e<f.length;e++){var g=b("[name='"+f[e]+"']");if(g.length==0){g=b("#"+f[e])}var c=g.closest(".form-group");b(c).find(".help-block").remove();var h=b(c).find("div:first");if(h.length==0){h=b(c)}h.append('<div class="help-block">'+d[f[e]]+"</div>");b(c).addClass("has-error")}}})(jQuery);$(document).on("ready",function(){$(".ajax-form").on("submit",function(b){b.preventDefault()})});$(document).on("ajaxPageLoad",function(){$(".ajax-form").on("submit",function(b){b.preventDefault()})});!function(d,c){"function"==typeof define&&define.amd?define(["jquery"],c):"object"==typeof exports?module.exports=c(require("jquery")):d.bootbox=c(d.jQuery)}(this,function a(G,F){function E(d){var c=r[t.locale];return c?c[d]:r.en[d]}function D(b,h,g){b.stopPropagation(),b.preventDefault();var f=G.isFunction(g)&&g.call(h,b)===!1;f||h.modal("hide")}function C(e){var d,f=0;for(d in e){f++}return f}function B(b,f){var e=0;G.each(b,function(d,c){f(d,c,e++)})}function A(b){var f,e;if("object"!=typeof b){throw new Error("Please supply an object of options")}if(!b.message){throw new Error("Please specify a message")}return b=G.extend({},t,b),b.buttons||(b.buttons={}),f=b.buttons,e=C(f),B(f,function(c,g,d){if(G.isFunction(g)&&(g=f[c]={callback:g}),"object"!==G.type(g)){throw new Error("button with key "+c+" must be an object")}g.label||(g.label=c),g.className||(g.className=2>=e&&d===e-1?"btn-primary":"btn-default")}),b}function z(f,e){var h=f.length,g={};if(1>h||h>2){throw new Error("Invalid argument length")}return 2===h||"string"==typeof f[0]?(g[e[0]]=f[0],g[e[1]]=f[1]):g=f[0],g}function y(b,f,e){return G.extend(!0,{},b,z(f,e))}function x(g,f,j,i){var h={className:"bootbox-"+g,buttons:w.apply(null,f)};return v(y(h,i,j),f)}function w(){for(var h={},d=0,l=arguments.length;l>d;d++){var k=arguments[d],j=k.toLowerCase(),i=k.toUpperCase();h[j]={label:E(i)}}return h}function v(e,c){var f={};return B(c,function(g,d){f[d]=!0}),B(e.buttons,function(b){if(f[b]===F){throw new Error("button key "+b+" is not allowed (options are "+c.join("\n")+")")}}),e}var u={dialog:"<div class='bootbox modal' tabindex='-1' role='dialog'><div class='modal-dialog'><div class='modal-content'><div class='modal-body'><div class='bootbox-body'></div></div></div></div></div>",header:"<div class='modal-header'><h4 class='modal-title'></h4></div>",footer:"<div class='modal-footer'></div>",closeButton:"<button type='button' class='bootbox-close-button close' data-dismiss='modal' aria-hidden='true'>&times;</button>",form:"<form class='bootbox-form'></form>",inputs:{text:"<input class='bootbox-input bootbox-input-text form-control' autocomplete=off type=text />",textarea:"<textarea class='bootbox-input bootbox-input-textarea form-control'></textarea>",email:"<input class='bootbox-input bootbox-input-email form-control' autocomplete='off' type='email' />",select:"<select class='bootbox-input bootbox-input-select form-control'></select>",checkbox:"<div class='checkbox'><label><input class='bootbox-input bootbox-input-checkbox' type='checkbox' /></label></div>",date:"<input class='bootbox-input bootbox-input-date form-control' autocomplete=off type='date' />",time:"<input class='bootbox-input bootbox-input-time form-control' autocomplete=off type='time' />",number:"<input class='bootbox-input bootbox-input-number form-control' autocomplete=off type='number' />",password:"<input class='bootbox-input bootbox-input-password form-control' autocomplete='off' type='password' />"}},t={locale:"en",backdrop:"static",animate:!0,className:null,closeButton:!0,show:!0,container:"body"},s={};s.alert=function(){var b;if(b=x("alert",["ok"],["message","callback"],arguments),b.callback&&!G.isFunction(b.callback)){throw new Error("alert requires callback property to be a function when provided")}return b.buttons.ok.callback=b.onEscape=function(){return G.isFunction(b.callback)?b.callback.call(this):!0},s.dialog(b)},s.confirm=function(){var b;if(b=x("confirm",["cancel","confirm"],["message","callback"],arguments),b.buttons.cancel.callback=b.onEscape=function(){return b.callback.call(this,!1)},b.buttons.confirm.callback=function(){return b.callback.call(this,!0)},!G.isFunction(b.callback)){throw new Error("confirm requires a callback")}return s.dialog(b)},s.prompt=function(){var H,p,n,m,l,j,g;if(m=G(u.form),p={className:"bootbox-prompt",buttons:w("cancel","confirm"),value:"",inputType:"text"},H=v(y(p,arguments,["title","callback"]),["cancel","confirm"]),j=H.show===F?!0:H.show,H.message=m,H.buttons.cancel.callback=H.onEscape=function(){return H.callback.call(this,null)},H.buttons.confirm.callback=function(){var f;switch(H.inputType){case"text":case"textarea":case"email":case"select":case"date":case"time":case"number":case"password":f=l.val();break;case"checkbox":var e=l.find("input:checked");f=[],B(e,function(h,i){f.push(G(i).val())})}return H.callback.call(this,f)},H.show=!1,!H.title){throw new Error("prompt requires a title")}if(!G.isFunction(H.callback)){throw new Error("prompt requires a callback")}if(!u.inputs[H.inputType]){throw new Error("invalid prompt type")}switch(l=G(u.inputs[H.inputType]),H.inputType){case"text":case"textarea":case"email":case"date":case"time":case"number":case"password":l.val(H.value);break;case"select":var c={};if(g=H.inputOptions||[],!G.isArray(g)){throw new Error("Please pass an array of input options")}if(!g.length){throw new Error("prompt with select requires options")}B(g,function(f,i){var h=l;if(i.value===F||i.text===F){throw new Error("given options in wrong format")}i.group&&(c[i.group]||(c[i.group]=G("<optgroup/>").attr("label",i.group)),h=c[i.group]),h.append("<option value='"+i.value+"'>"+i.text+"</option>")}),B(c,function(e,d){l.append(d)}),l.val(H.value);break;case"checkbox":var b=G.isArray(H.value)?H.value:[H.value];if(g=H.inputOptions||[],!g.length){throw new Error("prompt with checkbox requires options")}if(!g[0].value||!g[0].text){throw new Error("given options in wrong format")}l=G("<div/>"),B(g,function(i,h){var f=G(u.inputs[H.inputType]);f.find("input").attr("value",h.value),f.find("label").append(h.text),B(b,function(e,d){d===h.value&&f.find("input").prop("checked",!0)}),l.append(f)})}return H.placeholder&&l.attr("placeholder",H.placeholder),H.pattern&&l.attr("pattern",H.pattern),H.maxlength&&l.attr("maxlength",H.maxlength),m.append(l),m.on("submit",function(d){d.preventDefault(),d.stopPropagation(),n.find(".btn-primary").click()}),n=s.dialog(H),n.off("shown.bs.modal"),n.on("shown.bs.modal",function(){l.focus()}),j===!0&&n.modal("show"),n},s.dialog=function(e){e=A(e);var p=G(u.dialog),o=p.find(".modal-dialog"),n=p.find(".modal-body"),h=e.buttons,g="",c={onEscape:e.onEscape};if(G.fn.modal===F){throw new Error("$.fn.modal is not defined; please double check you have included the Bootstrap JavaScript library. See http://getbootstrap.com/javascript/ for more details.")}if(B(h,function(f,d){g+="<button data-bb-handler='"+f+"' type='button' class='btn "+d.className+"'>"+d.label+"</button>",c[f]=d.callback}),n.find(".bootbox-body").html(e.message),e.animate===!0&&p.addClass("fade"),e.className&&p.addClass(e.className),"large"===e.size?o.addClass("modal-lg"):"small"===e.size&&o.addClass("modal-sm"),e.title&&n.before(u.header),e.closeButton){var b=G(u.closeButton);e.title?p.find(".modal-header").prepend(b):b.css("margin-top","-10px").prependTo(n)}return e.title&&p.find(".modal-title").html(e.title),g.length&&(n.after(u.footer),p.find(".modal-footer").html(g)),p.on("hidden.bs.modal",function(d){d.target===this&&p.remove()}),p.on("shown.bs.modal",function(){p.find(".btn-primary:first").focus()}),"static"!==e.backdrop&&p.on("click.dismiss.bs.modal",function(d){p.children(".modal-backdrop").length&&(d.currentTarget=p.children(".modal-backdrop").get(0)),d.target===d.currentTarget&&p.trigger("escape.close.bb")}),p.on("escape.close.bb",function(d){c.onEscape&&D(d,p,c.onEscape)}),p.on("click",".modal-footer button",function(d){var f=G(this).data("bb-handler");D(d,p,c[f])}),p.on("click",".bootbox-close-button",function(d){D(d,p,c.onEscape)}),p.on("keyup",function(d){27===d.which&&p.trigger("escape.close.bb")}),G(e.container).append(p),p.modal({backdrop:e.backdrop?"static":!1,keyboard:!1,show:!1}),e.show&&p.modal("show"),p},s.setDefaults=function(){var b={};2===arguments.length?b[arguments[0]]=arguments[1]:b=arguments[0],G.extend(t,b)},s.hideAll=function(){return G(".bootbox").modal("hide"),s};var r={bg_BG:{OK:"ÐžÐº",CANCEL:"ÐžÑ‚ÐºÐ°Ð·",CONFIRM:"ÐŸÐ¾Ñ‚Ð²ÑŠÑ€Ð¶Ð´Ð°Ð²Ð°Ð¼"},br:{OK:"OK",CANCEL:"Cancelar",CONFIRM:"Sim"},cs:{OK:"OK",CANCEL:"ZruÅ¡it",CONFIRM:"Potvrdit"},da:{OK:"OK",CANCEL:"Annuller",CONFIRM:"Accepter"},de:{OK:"OK",CANCEL:"Abbrechen",CONFIRM:"Akzeptieren"},el:{OK:"Î•Î½Ï„Î¬Î¾ÎµÎ¹",CANCEL:"Î‘ÎºÏÏÏ‰ÏƒÎ·",CONFIRM:"Î•Ï€Î¹Î²ÎµÎ²Î±Î¯Ï‰ÏƒÎ·"},en:{OK:"OK",CANCEL:"Cancel",CONFIRM:"OK"},es:{OK:"OK",CANCEL:"Cancelar",CONFIRM:"Aceptar"},et:{OK:"OK",CANCEL:"Katkesta",CONFIRM:"OK"},fa:{OK:"Ù‚Ø¨ÙˆÙ„",CANCEL:"Ù„ØºÙˆ",CONFIRM:"ØªØ§ÛŒÛŒØ¯"},fi:{OK:"OK",CANCEL:"Peruuta",CONFIRM:"OK"},fr:{OK:"OK",CANCEL:"Annuler",CONFIRM:"D'accord"},he:{OK:"××™×©×•×¨",CANCEL:"×‘×™×˜×•×œ",CONFIRM:"××™×©×•×¨"},hu:{OK:"OK",CANCEL:"MÃ©gsem",CONFIRM:"MegerÅ‘sÃt"},hr:{OK:"OK",CANCEL:"Odustani",CONFIRM:"Potvrdi"},id:{OK:"OK",CANCEL:"Batal",CONFIRM:"OK"},it:{OK:"OK",CANCEL:"Annulla",CONFIRM:"Conferma"},ja:{OK:"OK",CANCEL:"ã‚ãƒ£ãƒ³ã‚»ãƒ«",CONFIRM:"ç¢ºèª"},lt:{OK:"Gerai",CANCEL:"AtÅ¡aukti",CONFIRM:"Patvirtinti"},lv:{OK:"Labi",CANCEL:"Atcelt",CONFIRM:"ApstiprinÄt"},nl:{OK:"OK",CANCEL:"Annuleren",CONFIRM:"Accepteren"},no:{OK:"OK",CANCEL:"Avbryt",CONFIRM:"OK"},pl:{OK:"OK",CANCEL:"Anuluj",CONFIRM:"PotwierdÅº"},pt:{OK:"OK",CANCEL:"Cancelar",CONFIRM:"Confirmar"},ru:{OK:"OK",CANCEL:"ÐžÑ‚Ð¼ÐµÐ½Ð°",CONFIRM:"ÐŸÑ€Ð¸Ð¼ÐµÐ½Ð¸Ñ‚ÑŒ"},sq:{OK:"OK",CANCEL:"Anulo",CONFIRM:"Prano"},sv:{OK:"OK",CANCEL:"Avbryt",CONFIRM:"OK"},th:{OK:"à¸•à¸à¸¥à¸‡",CANCEL:"à¸¢à¸à¹€à¸¥à¸´à¸",CONFIRM:"à¸¢à¸·à¸™à¸¢à¸±à¸™"},tr:{OK:"Tamam",CANCEL:"Ä°ptal",CONFIRM:"Onayla"},zh_CN:{OK:"OK",CANCEL:"å–æ¶ˆ",CONFIRM:"ç¡®è®¤"},zh_TW:{OK:"OK",CANCEL:"å–æ¶ˆ",CONFIRM:"ç¢ºèª"}};return s.addLocale=function(b,d){return G.each(["OK","CANCEL","CONFIRM"],function(e,c){if(!d[c]){throw new Error("Please supply a translation for '"+c+"'")}}),r[b]={OK:d.OK,CANCEL:d.CANCEL,CONFIRM:d.CONFIRM},s},s.removeLocale=function(b){return delete r[b],s},s.setLocale=function(b){return s.setDefaults("locale",b)},s.init=function(b){return a(b||G)},s});!function(b){b(["jquery"],function(c){return function(){function Q(e,d,f){return D({type:x.error,iconClass:C().iconClasses.error,message:e,optionsOverride:f,title:d})}function P(d,e){return d||(d=C()),A=c("#"+d.containerId),A.length?A:(e&&(A=G(d)),A)}function O(e,d,f){return D({type:x.info,iconClass:C().iconClasses.info,message:e,optionsOverride:f,title:d})}function N(d){z=d}function M(e,d,f){return D({type:x.success,iconClass:C().iconClasses.success,message:e,optionsOverride:f,title:d})}function L(e,d,f){return D({type:x.warning,iconClass:C().iconClasses.warning,message:e,optionsOverride:f,title:d})}function K(e){var d=C();A||P(d),H(e,d)||I(d)}function J(e){var f=C();return A||P(f),e&&0===c(":focus",e).length?void B(e):void (A.children().length&&A.remove())}function I(e){for(var g=A.children(),f=g.length-1;f>=0;f--){H(c(g[f]),e)}}function H(d,e){return d&&0===c(":focus",d).length?(d[e.hideMethod]({duration:e.hideDuration,easing:e.hideEasing,complete:function(){B(d)}}),!0):!1}function G(d){return A=c("<div/>").attr("id",d.containerId).addClass(d.positionClass).attr("aria-live","polite").attr("role","alert"),A.appendTo(c(d.target)),A}function F(){return{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:void 0,hideMethod:"fadeOut",hideDuration:1000,hideEasing:"swing",onHidden:void 0,extendedTimeOut:1000,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5000,titleClass:"toast-title",messageClass:"toast-message",target:"body",closeHtml:"<button>&times;</button>",newestOnTop:!0}}function E(d){z&&z(d)}function D(U){function T(d){return !c(":focus",s).length||d?s[v.hideMethod]({duration:v.hideDuration,easing:v.hideEasing,complete:function(){B(s),v.onHidden&&"hidden"!==n.state&&v.onHidden(),n.state="hidden",n.endTime=new Date,E(n)}}):void 0}function S(){(v.timeOut>0||v.extendedTimeOut>0)&&(t=setTimeout(T,v.extendedTimeOut))}function R(){clearTimeout(t),s.stop(!0,!0)[v.showMethod]({duration:v.showDuration,easing:v.showEasing})}var v=C(),u=U.iconClass||v.iconClass;"undefined"!=typeof U.optionsOverride&&(v=c.extend(v,U.optionsOverride),u=U.optionsOverride.iconClass||u),y++,A=P(v,!0);var t=null,s=c("<div/>"),r=c("<div/>"),q=c("<div/>"),p=c(v.closeHtml),n={toastId:y,state:"visible",startTime:new Date,options:v,map:U};return U.iconClass&&s.addClass(v.toastClass).addClass(u),U.title&&(r.append(U.title).addClass(v.titleClass),s.append(r)),U.message&&(q.append(U.message).addClass(v.messageClass),s.append(q)),v.closeButton&&(p.addClass("toast-close-button").attr("role","button"),s.prepend(p)),s.hide(),v.newestOnTop?A.prepend(s):A.append(s),s[v.showMethod]({duration:v.showDuration,easing:v.showEasing,complete:v.onShown}),v.timeOut>0&&(t=setTimeout(T,v.timeOut)),s.hover(R,S),!v.onclick&&v.tapToDismiss&&s.click(T),v.closeButton&&p&&p.click(function(d){d.stopPropagation?d.stopPropagation():void 0!==d.cancelBubble&&d.cancelBubble!==!0&&(d.cancelBubble=!0),T(!0)}),v.onclick&&s.click(function(){v.onclick(),T()}),E(n),v.debug&&console&&console.log(n),s}function C(){return c.extend({},F(),w.options)}function B(d){A||(A=P()),d.is(":visible")||(d.remove(),d=null,0===A.children().length&&A.remove())}var A,z,y=0,x={error:"error",info:"info",success:"success",warning:"warning"},w={clear:K,remove:J,error:Q,getContainer:P,info:O,options:{},subscribe:N,success:M,version:"2.0.3",warning:L};return w}()})}("function"==typeof define&&define.amd?define:function(d,c){"undefined"!=typeof module&&module.exports?module.exports=c(require("jquery")):window.toastr=c(window.jQuery)});
/*!
 * jQuery blockUI plugin
 * Version 2.70.0-2014.11.23
 * Requires jQuery v1.7 or later
 *
 * Examples at: http://malsup.com/jquery/block/
 * Copyright (c) 2007-2013 M. Alsup
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 *
 * Thanks to Amir-Hossein Sobhi for some excellent contributions!
 */
;!function(){function b(w){function C(L,R){var N,Z,X=L==window,p=R&&void 0!==R.message?R.message:void 0;if(R=w.extend({},w.blockUI.defaults,R||{}),!R.ignoreIfBlocked||!w(L).data("blockUI.isBlocked")){if(R.overlayCSS=w.extend({},w.blockUI.defaults.overlayCSS,R.overlayCSS||{}),N=w.extend({},w.blockUI.defaults.css,R.css||{}),R.onOverlayClick&&(R.overlayCSS.cursor="pointer"),Z=w.extend({},w.blockUI.defaults.themedCSS,R.themedCSS||{}),p=void 0===p?R.message:p,X&&h&&j(window,{fadeOut:0}),p&&"string"!=typeof p&&(p.parentNode||p.jquery)){var V=p.jquery?p[0]:p,J={};w(L).data("blockUI.history",J),J.el=V,J.parent=V.parentNode,J.display=V.style.display,J.position=V.style.position,J.parent&&J.parent.removeChild(V)}w(L).data("blockUI.onUnblock",R.onUnblock);var aa,r,G,d,u=R.baseZ;aa=w(g||R.forceIframe?'<iframe class="blockUI" style="z-index:'+u+++';display:none;border:none;margin:0;padding:0;position:absolute;width:100%;height:100%;top:0;left:0" src="'+R.iframeSrc+'"></iframe>':'<div class="blockUI" style="display:none"></div>'),r=w(R.theme?'<div class="blockUI blockOverlay ui-widget-overlay" style="z-index:'+u+++';display:none"></div>':'<div class="blockUI blockOverlay" style="z-index:'+u+++';display:none;border:none;margin:0;padding:0;width:100%;height:100%;top:0;left:0"></div>'),R.theme&&X?(d='<div class="blockUI '+R.blockMsgClass+' blockPage ui-dialog ui-widget ui-corner-all" style="z-index:'+(u+10)+';display:none;position:fixed">',R.title&&(d+='<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(R.title||"&nbsp;")+"</div>"),d+='<div class="ui-widget-content ui-dialog-content"></div>',d+="</div>"):R.theme?(d='<div class="blockUI '+R.blockMsgClass+' blockElement ui-dialog ui-widget ui-corner-all" style="z-index:'+(u+10)+';display:none;position:absolute">',R.title&&(d+='<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(R.title||"&nbsp;")+"</div>"),d+='<div class="ui-widget-content ui-dialog-content"></div>',d+="</div>"):d=X?'<div class="blockUI '+R.blockMsgClass+' blockPage" style="z-index:'+(u+10)+';display:none;position:fixed"></div>':'<div class="blockUI '+R.blockMsgClass+' blockElement" style="z-index:'+(u+10)+';display:none;position:absolute"></div>',G=w(d),p&&(R.theme?(G.css(Z),G.addClass("ui-widget-content")):G.css(N)),R.theme||r.css(R.overlayCSS),r.css("position",X?"fixed":"absolute"),(g||R.forceIframe)&&aa.css("opacity",0);var P=[aa,r,G],f=w(X?"body":L);w.each(P,function(){this.appendTo(f)}),R.theme&&R.draggable&&w.fn.draggable&&G.draggable({handle:".ui-dialog-titlebar",cancel:"li"});var i=v&&(!w.support.boxModel||w("object,embed",X?null:L).length>0);if(B||i){if(X&&R.allowBodyStretch&&w.support.boxModel&&w("html,body").css("height","100%"),(B||!w.support.boxModel)&&!X){var K=x(L,"borderTopWidth"),e=x(L,"borderLeftWidth"),l=K?"(0 - "+K+")":0,Q=e?"(0 - "+e+")":0}w.each(P,function(I,E){var M=E[0].style;if(M.position="absolute",2>I){X?M.setExpression("height","Math.max(document.body.scrollHeight, document.body.offsetHeight) - (jQuery.support.boxModel?0:"+R.quirksmodeOffsetHack+') + "px"'):M.setExpression("height",'this.parentNode.offsetHeight + "px"'),X?M.setExpression("width",'jQuery.support.boxModel && document.documentElement.clientWidth || document.body.clientWidth + "px"'):M.setExpression("width",'this.parentNode.offsetWidth + "px"'),Q&&M.setExpression("left",Q),l&&M.setExpression("top",l)}else{if(R.centerY){X&&M.setExpression("top",'(document.documentElement.clientHeight || document.body.clientHeight) / 2 - (this.offsetHeight / 2) + (blah = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "px"'),M.marginTop=0}else{if(!R.centerY&&X){var n=R.css&&R.css.top?parseInt(R.css.top,10):0,H="((document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "+n+') + "px"';M.setExpression("top",H)}}}})}if(p&&(R.theme?G.find(".ui-widget-content").append(p):G.append(p),(p.jquery||p.nodeType)&&w(p).show()),(g||R.forceIframe)&&R.showOverlay&&aa.show(),R.fadeIn){var Y=R.onBlock?R.onBlock:y,F=R.showOverlay&&!p?Y:y,o=p?Y:y;R.showOverlay&&r._fadeIn(R.fadeIn,F),p&&G._fadeIn(R.fadeIn,o)}else{R.showOverlay&&r.show(),p&&G.show(),R.onBlock&&R.onBlock.bind(G)()}if(q(1,L,R),X?(h=G[0],z=w(R.focusableElements,h),R.focusInput&&setTimeout(m,20)):A(G[0],R.centerX,R.centerY),R.timeout){var c=setTimeout(function(){X?w.unblockUI(R):w(L).unblock(R)},R.timeout);w(L).data("blockUI.timeout",c)}}}function j(i,E){var n,f=i==window,e=w(i),u=e.data("blockUI.history"),F=e.data("blockUI.timeout");F&&(clearTimeout(F),e.removeData("blockUI.timeout")),E=w.extend({},w.blockUI.defaults,E||{}),q(0,i,E),null===E.onUnblock&&(E.onUnblock=e.data("blockUI.onUnblock"),e.removeData("blockUI.onUnblock"));var p;p=f?w("body").children().filter(".blockUI").add("body > .blockUI"):e.find(">.blockUI"),E.cursorReset&&(p.length>1&&(p[1].style.cursor=E.cursorReset),p.length>2&&(p[2].style.cursor=E.cursorReset)),f&&(h=z=null),E.fadeOut?(n=p.length,p.stop().fadeOut(E.fadeOut,function(){0===--n&&k(p,u,E,i)})):k(p,u,E,i)}function k(p,E,F,f){var r=w(f);if(!r.data("blockUI.isBlocked")){p.each(function(){this.parentNode&&this.parentNode.removeChild(this)}),E&&E.el&&(E.el.style.display=E.display,E.el.style.position=E.position,E.el.style.cursor="default",E.parent&&E.parent.appendChild(E.el),r.removeData("blockUI.history")),r.data("blockUI.static")&&r.css("position","static"),"function"==typeof F.onUnblock&&F.onUnblock(f,F);var e=w(document.body),c=e.width(),u=e[0].style.width;e.width(c-1).width(c),e[0].style.width=u}}function q(f,p,r){var e=p==window,d=w(p);if((f||(!e||h)&&(e||d.data("blockUI.isBlocked")))&&(d.data("blockUI.isBlocked",f),e&&r.bindEvents&&(!f||r.showOverlay))){var c="mousedown mouseup keydown keypress keyup touchstart touchend touchmove";f?w(document).bind(c,r,D):w(document).unbind(c,D)}}function D(e){if("keydown"===e.type&&e.keyCode&&9==e.keyCode&&h&&e.data.constrainTabKey){var l=z,p=!e.shiftKey&&e.target===l[l.length-1],d=e.shiftKey&&e.target===l[0];if(p||d){return setTimeout(function(){m(d)},10),!1}}var f=e.data,c=w(e.target);return c.hasClass("blockOverlay")&&f.onOverlayClick&&f.onOverlayClick(e),c.parents("div."+f.blockMsgClass).length>0?!0:0===c.parents().children().filter("div.blockUI").length}function m(d){if(z){var c=z[d===!0?z.length-1:0];c&&c.focus()}}function A(r,f,u){var E=r.parentNode,d=r.style,p=(E.offsetWidth-r.offsetWidth)/2-x(E,"borderLeftWidth"),c=(E.offsetHeight-r.offsetHeight)/2-x(E,"borderTopWidth");f&&(d.left=p>0?p+"px":"0"),u&&(d.top=c>0?c+"px":"0")}function x(c,d){return parseInt(w.css(c,d),10)||0}w.fn._fadeIn=w.fn.fadeIn;var y=w.noop||function(){},g=/MSIE/.test(navigator.userAgent),B=/MSIE 6.0/.test(navigator.userAgent)&&!/MSIE 8.0/.test(navigator.userAgent),v=(document.documentMode||0,w.isFunction(document.createElement("div").style.setExpression));w.blockUI=function(c){C(window,c)},w.unblockUI=function(c){j(window,c)},w.growlUI=function(e,p,r,d){var f=w('<div class="growlUI"></div>');e&&f.append("<h1>"+e+"</h1>"),p&&f.append("<h2>"+p+"</h2>"),void 0===r&&(r=3000);var c=function(i){i=i||{},w.blockUI({message:f,fadeIn:"undefined"!=typeof i.fadeIn?i.fadeIn:700,fadeOut:"undefined"!=typeof i.fadeOut?i.fadeOut:1000,timeout:"undefined"!=typeof i.timeout?i.timeout:r,centerY:!1,showOverlay:!1,onUnblock:d,css:w.blockUI.defaults.growlCSS})};c();f.css("opacity");f.mouseover(function(){c({fadeIn:0,timeout:30000});var i=w(".blockMsg");i.stop(),i.fadeTo(300,1)}).mouseout(function(){w(".blockMsg").fadeOut(1000)})},w.fn.block=function(c){if(this[0]===window){return w.blockUI(c),this}var d=w.extend({},w.blockUI.defaults,c||{});return this.each(function(){var e=w(this);d.ignoreIfBlocked&&e.data("blockUI.isBlocked")||e.unblock({fadeOut:0})}),this.each(function(){"static"==w.css(this,"position")&&(this.style.position="relative",w(this).data("blockUI.static",!0)),this.style.zoom=1,C(this,c)})},w.fn.unblock=function(c){return this[0]===window?(w.unblockUI(c),this):this.each(function(){j(this,c)})},w.blockUI.version=2.7,w.blockUI.defaults={message:"<h1>Please wait...</h1>",title:null,draggable:!0,theme:!1,css:{padding:0,margin:0,width:"30%",top:"40%",left:"35%",textAlign:"center",color:"#000",border:"3px solid #aaa",backgroundColor:"#fff",cursor:"wait"},themedCSS:{width:"30%",top:"40%",left:"35%"},overlayCSS:{backgroundColor:"#000",opacity:0.6,cursor:"wait"},cursorReset:"default",growlCSS:{width:"350px",top:"10px",left:"",right:"10px",border:"none",padding:"5px",opacity:0.6,cursor:"default",color:"#fff",backgroundColor:"#000","-webkit-border-radius":"10px","-moz-border-radius":"10px","border-radius":"10px"},iframeSrc:/^https/i.test(window.location.href||"")?"javascript:false":"about:blank",forceIframe:!1,baseZ:1000,centerX:!0,centerY:!0,allowBodyStretch:!0,bindEvents:!0,constrainTabKey:!0,fadeIn:200,fadeOut:400,timeout:0,showOverlay:!0,focusInput:!0,focusableElements:":input:enabled:visible",onBlock:null,onUnblock:null,onOverlayClick:null,quirksmodeOffsetHack:4,blockMsgClass:"blockMsg",ignoreIfBlocked:!1};var h=null,z=[]}"function"==typeof define&&define.amd&&define.amd.jQuery?define(["jquery"],b):b(jQuery)}();
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AgeVerification;

class AgeVerificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Check if age verification record already exists
        $existingRecord = AgeVerification::first();
        
        if (!$existingRecord) {
            AgeVerification::create([
                'age_verification_on_off' => 0, // Disabled by default
                'popup_type' => 1, // Yes/No type
                'min_age' => 18, // Default minimum age
                'age_verification_text' => 'You must be 18 or older to access this website.',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            echo "Age verification settings created successfully!\n";
        } else {
            echo "Age verification settings already exist.\n";
        }
    }
}
